// Auto generated code by esphome
// ========== AUTO GENERATED INCLUDE BLOCK BEGIN ===========
#include "esphome.h"
using namespace esphome;
using std::isnan;
using std::min;
using std::max;
using namespace sensor;
using namespace text_sensor;
using namespace binary_sensor;
using namespace switch_;
logger::Logger *logger_logger_id;
web_server_base::WebServerBase *web_server_base_webserverbase_id;
wifi::WiFiComponent *wifi_wificomponent_id;
mdns::MDNSComponent *mdns_mdnscomponent_id;
esphome::ESPHomeOTAComponent *esphome_esphomeotacomponent_id;
safe_mode::SafeModeComponent *safe_mode_safemodecomponent_id;
Automation<> *automation_id;
LambdaAction<> *lambdaaction_id;
Automation<> *automation_id_2;
LambdaAction<> *lambdaaction_id_2;
api::APIServer *api_apiserver_id;
using namespace api;
web_server::WebServer *web_server_webserver_id;
const uint8_t ESPHOME_WEBSERVER_INDEX_H<PERSON>L[174] PROGMEM = {60, 33, 68, 79, 67, 84, 89, 80, 69, 32, 104, 116, 109, 108, 62, 60, 104, 116, 109, 108, 62, 60, 104, 101, 97, 100, 62, 60, 109, 101, 116, 97, 32, 99, 104, 97, 114, 115, 101, 116, 61, 85, 84, 70, 45, 56, 62, 60, 108, 105, 110, 107, 32, 114, 101, 108, 61, 105, 99, 111, 110, 32, 104, 114, 101, 102, 61, 100, 97, 116, 97, 58, 62, 60, 47, 104, 101, 97, 100, 62, 60, 98, 111, 100, 121, 62, 60, 101, 115, 112, 45, 97, 112, 112, 62, 60, 47, 101, 115, 112, 45, 97, 112, 112, 62, 60, 115, 99, 114, 105, 112, 116, 32, 115, 114, 99, 61, 34, 104, 116, 116, 112, 115, 58, 47, 47, 111, 105, 46, 101, 115, 112, 104, 111, 109, 101, 46, 105, 111, 47, 118, 50, 47, 119, 119, 119, 46, 106, 115, 34, 62, 60, 47, 115, 99, 114, 105, 112, 116, 62, 60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62};
const size_t ESPHOME_WEBSERVER_INDEX_HTML_SIZE = 174;
using namespace json;
preferences::IntervalSyncer *preferences_intervalsyncer_id;
using namespace uart;
uart::ESP32ArduinoUARTComponent *uart_bus;
esp32::ESP32InternalGPIOPin *esp32_esp32internalgpiopin_id;
esp32::ESP32InternalGPIOPin *esp32_esp32internalgpiopin_id_2;
template_::TemplateSensor *voc_tvoc;
template_::TemplateSensor *voc_hcho;
template_::TemplateSensor *voc_co2;
wifi_signal::WiFiSignalSensor *wifi_signal_wifisignalsensor_id;
uptime::UptimeSecondsSensor *uptime_uptimesecondssensor_id;
internal_temperature::InternalTemperatureSensor *internal_temperature_internaltemperaturesensor_id;
interval::IntervalTrigger *interval_intervaltrigger_id;
Automation<> *automation_id_3;
wifi_info::SSIDWiFiInfo *wifi_info_ssidwifiinfo_id;
wifi_info::MacAddressWifiInfo *wifi_info_macaddresswifiinfo_id;
wifi_info::IPAddressWiFiInfo *wifi_info_ipaddresswifiinfo_id;
status::StatusBinarySensor *status_statusbinarysensor_id;
restart::RestartSwitch *restart_restartswitch_id;
globals::GlobalsComponent<std::vector<uint8_t>> *uart_buffer;
globals::GlobalsComponent<float> *last_tvoc;
globals::GlobalsComponent<float> *last_hcho;
globals::GlobalsComponent<float> *last_co2;
LambdaAction<> *lambdaaction_id_3;
#define yield() esphome::yield()
#define millis() esphome::millis()
#define micros() esphome::micros()
#define delay(x) esphome::delay(x)
#define delayMicroseconds(x) esphome::delayMicroseconds(x)
// ========== AUTO GENERATED INCLUDE BLOCK END ==========="

void setup() {
  // ========== AUTO GENERATED CODE BEGIN ===========
  App.reserve_text_sensor(3);
  App.reserve_switch(1);
  App.reserve_sensor(6);
  App.reserve_binary_sensor(1);
  // network:
  //   enable_ipv6: false
  //   min_ipv6_addr_count: 0
  // async_tcp:
  //   {}
  // esphome:
  //   name: myesp32c3
  //   friendly_name: VOC-CO2-HCHO ESP32-C3 Sensor
  //   min_version: 2025.7.2
  //   build_path: build\myesp32c3
  //   platformio_options: {}
  //   includes: []
  //   libraries: []
  //   name_add_mac_suffix: false
  //   debug_scheduler: false
  //   areas: []
  //   devices: []
  App.pre_setup("myesp32c3", "VOC-CO2-HCHO ESP32-C3 Sensor", "", __DATE__ ", " __TIME__, false);
  App.reserve_components(26);
  // sensor:
  // text_sensor:
  // binary_sensor:
  // switch:
  // logger:
  //   baud_rate: 115200
  //   level: INFO
  //   logs:
  //     voc_uart: INFO
  //     uart_debug: WARN
  //     uart: WARN
  //     wifi: WARN
  //     api: WARN
  //     ota: WARN
  //     web_server: WARN
  //   id: logger_logger_id
  //   tx_buffer_size: 512
  //   deassert_rts_dtr: false
  //   task_log_buffer_size: 768
  //   hardware_uart: USB_CDC
  logger_logger_id = new logger::Logger(115200, 512);
  logger_logger_id->create_pthread_key();
  logger_logger_id->init_log_buffer(768);
  logger_logger_id->set_log_level(ESPHOME_LOG_LEVEL_INFO);
  logger_logger_id->set_uart_selection(logger::UART_SELECTION_USB_CDC);
  logger_logger_id->pre_setup();
  logger_logger_id->set_log_level("voc_uart", ESPHOME_LOG_LEVEL_INFO);
  logger_logger_id->set_log_level("uart_debug", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("uart", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("wifi", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("api", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("ota", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_log_level("web_server", ESPHOME_LOG_LEVEL_WARN);
  logger_logger_id->set_component_source("logger");
  App.register_component(logger_logger_id);
  // web_server_base:
  //   id: web_server_base_webserverbase_id
  web_server_base_webserverbase_id = new web_server_base::WebServerBase();
  web_server_base_webserverbase_id->set_component_source("web_server_base");
  App.register_component(web_server_base_webserverbase_id);
  web_server_base::global_web_server_base = web_server_base_webserverbase_id;
  // wifi:
  //   ap:
  //     ssid: ESP32C3-VOC Fallback Hotspot
  //     password: '12345678'
  //     id: wifi_wifiap_id
  //     ap_timeout: 1min
  //   on_connect:
  //     then:
  //       - logger.log:
  //           format: WiFi连接成功！
  //           args: []
  //           tag: main
  //           level: DEBUG
  //         type_id: lambdaaction_id
  //     trigger_id: trigger_id
  //     automation_id: automation_id
  //   on_disconnect:
  //     then:
  //       - logger.log:
  //           format: WiFi连接断开！
  //           args: []
  //           tag: main
  //           level: DEBUG
  //         type_id: lambdaaction_id_2
  //     trigger_id: trigger_id_2
  //     automation_id: automation_id_2
  //   id: wifi_wificomponent_id
  //   domain: .local
  //   reboot_timeout: 15min
  //   power_save_mode: LIGHT
  //   fast_connect: false
  //   passive_scan: false
  //   enable_on_boot: true
  //   networks:
  //     - ssid: !secret 'wifi_ssid'
  //       password: !secret 'wifi_password'
  //       id: wifi_wifiap_id_2
  //       priority: 0.0
  //   use_address: myesp32c3.local
  wifi_wificomponent_id = new wifi::WiFiComponent();
  wifi_wificomponent_id->set_use_address("myesp32c3.local");
  {
  wifi::WiFiAP wifi_wifiap_id_2 = wifi::WiFiAP();
  wifi_wifiap_id_2.set_ssid("HOME");
  wifi_wifiap_id_2.set_password("nb9d30@24zd");
  wifi_wifiap_id_2.set_priority(0.0f);
  wifi_wificomponent_id->add_sta(wifi_wifiap_id_2);
  }
  {
  wifi::WiFiAP wifi_wifiap_id = wifi::WiFiAP();
  wifi_wifiap_id.set_ssid("ESP32C3-VOC Fallback Hotspot");
  wifi_wifiap_id.set_password("12345678");
  wifi_wificomponent_id->set_ap(wifi_wifiap_id);
  }
  wifi_wificomponent_id->set_ap_timeout(60000);
  wifi_wificomponent_id->set_reboot_timeout(900000);
  wifi_wificomponent_id->set_power_save_mode(wifi::WIFI_POWER_SAVE_LIGHT);
  wifi_wificomponent_id->set_fast_connect(false);
  wifi_wificomponent_id->set_passive_scan(false);
  wifi_wificomponent_id->set_enable_on_boot(true);
  wifi_wificomponent_id->set_component_source("wifi");
  App.register_component(wifi_wificomponent_id);
  // mdns:
  //   id: mdns_mdnscomponent_id
  //   disabled: false
  //   services: []
  mdns_mdnscomponent_id = new mdns::MDNSComponent();
  mdns_mdnscomponent_id->set_component_source("mdns");
  App.register_component(mdns_mdnscomponent_id);
  // ota:
  // ota.esphome:
  //   platform: esphome
  //   id: esphome_esphomeotacomponent_id
  //   version: 2
  //   port: 3232
  esphome_esphomeotacomponent_id = new esphome::ESPHomeOTAComponent();
  esphome_esphomeotacomponent_id->set_port(3232);
  esphome_esphomeotacomponent_id->set_component_source("esphome.ota");
  App.register_component(esphome_esphomeotacomponent_id);
  // safe_mode:
  //   id: safe_mode_safemodecomponent_id
  //   boot_is_good_after: 1min
  //   disabled: false
  //   num_attempts: 10
  //   reboot_timeout: 5min
  safe_mode_safemodecomponent_id = new safe_mode::SafeModeComponent();
  safe_mode_safemodecomponent_id->set_component_source("safe_mode");
  App.register_component(safe_mode_safemodecomponent_id);
  if (safe_mode_safemodecomponent_id->should_enter_safe_mode(10, 300000, 60000)) return;
  automation_id = new Automation<>(wifi_wificomponent_id->get_connect_trigger());
  lambdaaction_id = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "WiFi\350\277\236\346\216\245\346\210\220\345\212\237\357\274\201");
  });
  automation_id->add_actions({lambdaaction_id});
  automation_id_2 = new Automation<>(wifi_wificomponent_id->get_disconnect_trigger());
  lambdaaction_id_2 = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "WiFi\350\277\236\346\216\245\346\226\255\345\274\200\357\274\201");
  });
  automation_id_2->add_actions({lambdaaction_id_2});
  // api:
  //   id: api_apiserver_id
  //   port: 6053
  //   password: ''
  //   reboot_timeout: 15min
  //   batch_delay: 100ms
  //   custom_services: false
  api_apiserver_id = new api::APIServer();
  api_apiserver_id->set_component_source("api");
  App.register_component(api_apiserver_id);
  api_apiserver_id->set_port(6053);
  api_apiserver_id->set_reboot_timeout(900000);
  api_apiserver_id->set_batch_delay(100);
  // web_server:
  //   port: 80
  //   auth:
  //     username: admin
  //     password: !secret 'wifi_password'
  //   id: web_server_webserver_id
  //   version: 2
  //   enable_private_network_access: true
  //   web_server_base_id: web_server_base_webserverbase_id
  //   include_internal: false
  //   log: true
  //   css_url: ''
  //   js_url: https:oi.esphome.io/v2/www.js
  web_server_webserver_id = new web_server::WebServer(web_server_base_webserverbase_id);
  web_server_webserver_id->set_component_source("web_server");
  App.register_component(web_server_webserver_id);
  web_server_base_webserverbase_id->set_port(80);
  web_server_webserver_id->set_expose_log(true);
  web_server_base_webserverbase_id->set_auth_username("admin");
  web_server_base_webserverbase_id->set_auth_password("nb9d30@24zd");
  web_server_webserver_id->set_include_internal(false);
  // json:
  //   {}
  // esp32:
  //   board: airm2m_core_esp32c3
  //   framework:
  //     version: 3.1.3
  //     advanced:
  //       ignore_efuse_custom_mac: false
  //     source: pioarduino/framework-arduinoespressif32@https:github.com/espressif/arduino-esp32/releases/download/3.1.3/esp32-3.1.3.zip
  //     platform_version: https:github.com/pioarduino/platform-espressif32/releases/download/53.03.13/platform-espressif32.zip
  //     type: arduino
  //   flash_size: 4MB
  //   variant: ESP32C3
  //   cpu_frequency: 160MHZ
  setCpuFrequencyMhz(160);
  // preferences:
  //   id: preferences_intervalsyncer_id
  //   flash_write_interval: 60s
  preferences_intervalsyncer_id = new preferences::IntervalSyncer();
  preferences_intervalsyncer_id->set_write_interval(60000);
  preferences_intervalsyncer_id->set_component_source("preferences");
  App.register_component(preferences_intervalsyncer_id);
  // uart:
  //   id: uart_bus
  //   tx_pin:
  //     number: 0
  //     mode:
  //       output: true
  //       input: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //     id: esp32_esp32internalgpiopin_id
  //     inverted: false
  //     ignore_pin_validation_error: false
  //     ignore_strapping_warning: false
  //     drive_strength: 20.0
  //   rx_pin:
  //     number: 1
  //     mode:
  //       input: true
  //       output: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //     id: esp32_esp32internalgpiopin_id_2
  //     inverted: false
  //     ignore_pin_validation_error: false
  //     ignore_strapping_warning: false
  //     drive_strength: 20.0
  //   baud_rate: 9600
  //   data_bits: 8
  //   parity: NONE
  //   stop_bits: 1
  //   rx_buffer_size: 512
  uart_bus = new uart::ESP32ArduinoUARTComponent();
  uart_bus->set_component_source("uart");
  App.register_component(uart_bus);
  uart_bus->set_baud_rate(9600);
  esp32_esp32internalgpiopin_id = new esp32::ESP32InternalGPIOPin();
  esp32_esp32internalgpiopin_id->set_pin(::GPIO_NUM_0);
  esp32_esp32internalgpiopin_id->set_inverted(false);
  esp32_esp32internalgpiopin_id->set_drive_strength(::GPIO_DRIVE_CAP_2);
  esp32_esp32internalgpiopin_id->set_flags(gpio::Flags::FLAG_OUTPUT);
  uart_bus->set_tx_pin(esp32_esp32internalgpiopin_id);
  esp32_esp32internalgpiopin_id_2 = new esp32::ESP32InternalGPIOPin();
  esp32_esp32internalgpiopin_id_2->set_pin(::GPIO_NUM_1);
  esp32_esp32internalgpiopin_id_2->set_inverted(false);
  esp32_esp32internalgpiopin_id_2->set_drive_strength(::GPIO_DRIVE_CAP_2);
  esp32_esp32internalgpiopin_id_2->set_flags(gpio::Flags::FLAG_INPUT);
  uart_bus->set_rx_pin(esp32_esp32internalgpiopin_id_2);
  uart_bus->set_rx_buffer_size(512);
  uart_bus->set_stop_bits(1);
  uart_bus->set_data_bits(8);
  uart_bus->set_parity(uart::UART_CONFIG_PARITY_NONE);
  // sensor.template:
  //   platform: template
  //   name: TVOC
  //   id: voc_tvoc
  //   unit_of_measurement: mg/m³
  //   accuracy_decimals: 3
  //   device_class: volatile_organic_compounds
  //   state_class: measurement
  //   icon: mdi:chemical-weapon
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  voc_tvoc = new template_::TemplateSensor();
  App.register_sensor(voc_tvoc);
  voc_tvoc->set_name("TVOC");
  voc_tvoc->set_object_id("tvoc");
  voc_tvoc->set_disabled_by_default(false);
  voc_tvoc->set_icon("mdi:chemical-weapon");
  voc_tvoc->set_device_class("volatile_organic_compounds");
  voc_tvoc->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  voc_tvoc->set_unit_of_measurement("mg/m\302\263");
  voc_tvoc->set_accuracy_decimals(3);
  voc_tvoc->set_force_update(false);
  voc_tvoc->set_update_interval(60000);
  voc_tvoc->set_component_source("template.sensor");
  App.register_component(voc_tvoc);
  // sensor.template:
  //   platform: template
  //   name: Formaldehyde
  //   id: voc_hcho
  //   unit_of_measurement: mg/m³
  //   accuracy_decimals: 3
  //   device_class: volatile_organic_compounds
  //   state_class: measurement
  //   icon: mdi:molecule
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  voc_hcho = new template_::TemplateSensor();
  App.register_sensor(voc_hcho);
  voc_hcho->set_name("Formaldehyde");
  voc_hcho->set_object_id("formaldehyde");
  voc_hcho->set_disabled_by_default(false);
  voc_hcho->set_icon("mdi:molecule");
  voc_hcho->set_device_class("volatile_organic_compounds");
  voc_hcho->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  voc_hcho->set_unit_of_measurement("mg/m\302\263");
  voc_hcho->set_accuracy_decimals(3);
  voc_hcho->set_force_update(false);
  voc_hcho->set_update_interval(60000);
  voc_hcho->set_component_source("template.sensor");
  App.register_component(voc_hcho);
  // sensor.template:
  //   platform: template
  //   name: CO2
  //   id: voc_co2
  //   unit_of_measurement: mg/m³
  //   accuracy_decimals: 3
  //   device_class: carbon_dioxide
  //   state_class: measurement
  //   icon: mdi:molecule-co2
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  voc_co2 = new template_::TemplateSensor();
  App.register_sensor(voc_co2);
  voc_co2->set_name("CO2");
  voc_co2->set_object_id("co2");
  voc_co2->set_disabled_by_default(false);
  voc_co2->set_icon("mdi:molecule-co2");
  voc_co2->set_device_class("carbon_dioxide");
  voc_co2->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  voc_co2->set_unit_of_measurement("mg/m\302\263");
  voc_co2->set_accuracy_decimals(3);
  voc_co2->set_force_update(false);
  voc_co2->set_update_interval(60000);
  voc_co2->set_component_source("template.sensor");
  App.register_component(voc_co2);
  // sensor.wifi_signal:
  //   platform: wifi_signal
  //   name: WiFi Signal
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: wifi_signal_wifisignalsensor_id
  //   unit_of_measurement: dBm
  //   accuracy_decimals: 0
  //   device_class: signal_strength
  //   state_class: measurement
  //   entity_category: diagnostic
  wifi_signal_wifisignalsensor_id = new wifi_signal::WiFiSignalSensor();
  App.register_sensor(wifi_signal_wifisignalsensor_id);
  wifi_signal_wifisignalsensor_id->set_name("WiFi Signal");
  wifi_signal_wifisignalsensor_id->set_object_id("wifi_signal");
  wifi_signal_wifisignalsensor_id->set_disabled_by_default(false);
  wifi_signal_wifisignalsensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_signal_wifisignalsensor_id->set_device_class("signal_strength");
  wifi_signal_wifisignalsensor_id->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  wifi_signal_wifisignalsensor_id->set_unit_of_measurement("dBm");
  wifi_signal_wifisignalsensor_id->set_accuracy_decimals(0);
  wifi_signal_wifisignalsensor_id->set_force_update(false);
  wifi_signal_wifisignalsensor_id->set_update_interval(60000);
  wifi_signal_wifisignalsensor_id->set_component_source("wifi_signal.sensor");
  App.register_component(wifi_signal_wifisignalsensor_id);
  // sensor.uptime:
  //   platform: uptime
  //   name: Uptime
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: uptime_uptimesecondssensor_id
  //   unit_of_measurement: s
  //   icon: mdi:timer-outline
  //   accuracy_decimals: 0
  //   device_class: duration
  //   state_class: total_increasing
  //   entity_category: diagnostic
  //   type: seconds
  uptime_uptimesecondssensor_id = new uptime::UptimeSecondsSensor();
  App.register_sensor(uptime_uptimesecondssensor_id);
  uptime_uptimesecondssensor_id->set_name("Uptime");
  uptime_uptimesecondssensor_id->set_object_id("uptime");
  uptime_uptimesecondssensor_id->set_disabled_by_default(false);
  uptime_uptimesecondssensor_id->set_icon("mdi:timer-outline");
  uptime_uptimesecondssensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  uptime_uptimesecondssensor_id->set_device_class("duration");
  uptime_uptimesecondssensor_id->set_state_class(sensor::STATE_CLASS_TOTAL_INCREASING);
  uptime_uptimesecondssensor_id->set_unit_of_measurement("s");
  uptime_uptimesecondssensor_id->set_accuracy_decimals(0);
  uptime_uptimesecondssensor_id->set_force_update(false);
  uptime_uptimesecondssensor_id->set_update_interval(60000);
  uptime_uptimesecondssensor_id->set_component_source("uptime.sensor");
  App.register_component(uptime_uptimesecondssensor_id);
  // sensor.internal_temperature:
  //   platform: internal_temperature
  //   name: ESP32-C3 Internal Temperature
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: internal_temperature_internaltemperaturesensor_id
  //   unit_of_measurement: °C
  //   accuracy_decimals: 1
  //   device_class: temperature
  //   state_class: measurement
  //   entity_category: diagnostic
  internal_temperature_internaltemperaturesensor_id = new internal_temperature::InternalTemperatureSensor();
  App.register_sensor(internal_temperature_internaltemperaturesensor_id);
  internal_temperature_internaltemperaturesensor_id->set_name("ESP32-C3 Internal Temperature");
  internal_temperature_internaltemperaturesensor_id->set_object_id("esp32-c3_internal_temperature");
  internal_temperature_internaltemperaturesensor_id->set_disabled_by_default(false);
  internal_temperature_internaltemperaturesensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  internal_temperature_internaltemperaturesensor_id->set_device_class("temperature");
  internal_temperature_internaltemperaturesensor_id->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  internal_temperature_internaltemperaturesensor_id->set_unit_of_measurement("\302\260C");
  internal_temperature_internaltemperaturesensor_id->set_accuracy_decimals(1);
  internal_temperature_internaltemperaturesensor_id->set_force_update(false);
  internal_temperature_internaltemperaturesensor_id->set_update_interval(60000);
  internal_temperature_internaltemperaturesensor_id->set_component_source("internal_temperature.sensor");
  App.register_component(internal_temperature_internaltemperaturesensor_id);
  // interval:
  //   - interval: 2000ms
  //     then:
  //       - lambda: !lambda |-
  //            检查UART是否有可用数据
  //           int available = id(uart_bus).available();
  //           if (available > 0) {
  //             ESP_LOGD("voc_uart", "UART缓冲区有 %d 字节可用数据", available);
  //   
  //              读取所有可用数据到缓冲区
  //             uint8_t buffer[64];
  //             int bytes_read = 0;
  //   
  //              使用read_array方法读取数据
  //             bytes_read = id(uart_bus).read_array(buffer, std::min(available, 64));
  //   
  //             if (bytes_read > 0) {
  //                添加到全局缓冲区
  //               for (int i = 0; i < bytes_read; i++) {
  //                 id(uart_buffer).push_back(buffer[i]);
  //               }
  //   
  //                详细输出接收到的原始数据
  //               ESP_LOGI("voc_uart", "接收到 %d 字节数据:", bytes_read);
  //               std::string hex_data = "";
  //               for (int i = 0; i < bytes_read; i++) {
  //                 char hex_str[4];
  //                 sprintf(hex_str, "%02X ", buffer[i]);
  //                 hex_data += hex_str;
  //               }
  //               ESP_LOGI("voc_uart", "原始数据: %s", hex_data.c_str());
  //   
  //                查找完整的数据帧 (9字节，以0x2C 0xE4开头)
  //               auto &buf = id(uart_buffer);
  //               for (size_t i = 0; i <= buf.size() - 9; i++) {
  //                 if (buf[i] == 0x2C && buf[i + 1] == 0xE4) {
  //                   ESP_LOGI("voc_uart", "在位置 %d 找到帧头 0x2C 0xE4", i);
  //   
  //                    找到帧头，提取9字节数据帧
  //                   uint8_t frame[9];
  //                   for (int j = 0; j < 9; j++) {
  //                     frame[j] = buf[i + j];
  //                   }
  //   
  //                   ESP_LOGI("voc_uart", "提取的完整帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
  //                            frame[0], frame[1], frame[2], frame[3], frame[4],
  //                            frame[5], frame[6], frame[7], frame[8]);
  //   
  //                    验证校验和 (B1+B2+...+B8的低8位)
  //                   uint8_t checksum = 0;
  //                   for (int j = 0; j < 8; j++) {
  //                     checksum += frame[j];
  //                   }
  //   
  //                   if ((checksum & 0xFF) == frame[8]) {
  //                     ESP_LOGI("voc_uart", "找到有效数据帧，开始解析");
  //   
  //                      详细输出原始数据帧
  //                     ESP_LOGI("voc_uart", "原始数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
  //                              frame[0], frame[1], frame[2], frame[3], frame[4],
  //                              frame[5], frame[6], frame[7], frame[8]);
  //   
  //                      解析VOC数据
  //                      TVOC浓度 (mg/m³): (B3*256 + B4) × 0.001
  //                     uint16_t tvoc_raw = frame[2] * 256 + frame[3];
  //                     float tvoc = (float)tvoc_raw * 0.001;
  //   
  //                      甲醛浓度 (mg/m³): (B5*256 + B6) × 0.001
  //                     uint16_t hcho_raw = frame[4] * 256 + frame[5];
  //                     float hcho = (float)hcho_raw * 0.001;
  //   
  //                      CO₂浓度 (mg/m³): (B7*256 + B8) × 0.001
  //                     uint16_t co2_raw = frame[6] * 256 + frame[7];
  //                     float co2 = (float)co2_raw * 0.001;
  //   
  //                     ESP_LOGI("voc_uart", "解析结果 - TVOC原始值: %d, 甲醛原始值: %d, CO₂原始值: %d",
  //                              tvoc_raw, hcho_raw, co2_raw);
  //   
  //                      数据有效性检查和发布
  //                     if (tvoc >= 0.0 && tvoc <= 10.0) {   TVOC合理范围
  //                       if (id(last_tvoc) == -999.0 || abs(tvoc - id(last_tvoc)) >= 0.001) {
  //                         ESP_LOGI("voc_uart", "TVOC: %.3f mg/m³", tvoc);
  //                         id(voc_tvoc).publish_state(tvoc);
  //                         id(last_tvoc) = tvoc;
  //                       }
  //                     }
  //   
  //                     if (hcho >= 0.0 && hcho <= 1.0) {   甲醛合理范围
  //                       if (id(last_hcho) == -999.0 || abs(hcho - id(last_hcho)) >= 0.001) {
  //                         ESP_LOGI("voc_uart", "甲醛: %.3f mg/m³", hcho);
  //                         id(voc_hcho).publish_state(hcho);
  //                         id(last_hcho) = hcho;
  //                       }
  //                     }
  //   
  //                     if (co2 >= 0.0 && co2 <= 5.0) {   CO2合理范围
  //                       if (id(last_co2) == -999.0 || abs(co2 - id(last_co2)) >= 0.001) {
  //                         ESP_LOGI("voc_uart", "CO₂: %.3f mg/m³", co2);
  //                         id(voc_co2).publish_state(co2);
  //                         id(last_co2) = co2;
  //                       }
  //                     }
  //                   } else {
  //                      详细输出校验和错误的原始数据
  //                     ESP_LOGW("voc_uart", "校验和错误，丢弃数据帧");
  //                     ESP_LOGW("voc_uart", "原始数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
  //                              frame[0], frame[1], frame[2], frame[3], frame[4],
  //                              frame[5], frame[6], frame[7], frame[8]);
  //                     ESP_LOGW("voc_uart", "计算校验和: 0x%02X, 接收校验和: 0x%02X",
  //                              (checksum & 0xFF), frame[8]);
  //                     ESP_LOGW("voc_uart", "校验和计算过程: %02X+%02X+%02X+%02X+%02X+%02X+%02X+%02X = 0x%02X",
  //                              frame[0], frame[1], frame[2], frame[3], frame[4],
  //                              frame[5], frame[6], frame[7], checksum);
  //                   }
  //   
  //                    移除已处理的数据
  //                   buf.erase(buf.begin(), buf.begin() + i + 9);
  //                   break;
  //                 }
  //               }
  //   
  //                如果没有找到有效帧头，输出当前缓冲区内容用于调试
  //               if (buf.size() >= 9) {
  //                 bool found_frame = false;
  //                 for (size_t i = 0; i <= buf.size() - 9; i++) {
  //                   if (buf[i] == 0x2C && buf[i + 1] == 0xE4) {
  //                     found_frame = true;
  //                     break;
  //                   }
  //                 }
  //                 if (!found_frame) {
  //                   ESP_LOGW("voc_uart", "缓冲区中未找到有效帧头，当前缓冲区大小: %d", buf.size());
  //                   std::string debug_data = "";
  //                   for (size_t i = 0; i < std::min(buf.size(), (size_t)20); i++) {
  //                     char hex_str[4];
  //                     sprintf(hex_str, "%02X ", buf[i]);
  //                     debug_data += hex_str;
  //                   }
  //                   ESP_LOGW("voc_uart", "缓冲区前20字节: %s", debug_data.c_str());
  //                 }
  //               }
  //   
  //                清理过长的缓冲区
  //               if (buf.size() > 100) {
  //                 ESP_LOGW("voc_uart", "缓冲区过长(%d字节)，清空缓冲区", buf.size());
  //                 buf.clear();
  //               }
  //             }
  //           }
  //         type_id: lambdaaction_id_3
  //     trigger_id: trigger_id_3
  //     automation_id: automation_id_3
  //     id: interval_intervaltrigger_id
  //     startup_delay: 0s
  interval_intervaltrigger_id = new interval::IntervalTrigger();
  interval_intervaltrigger_id->set_component_source("interval");
  App.register_component(interval_intervaltrigger_id);
  automation_id_3 = new Automation<>(interval_intervaltrigger_id);
  // text_sensor.wifi_info:
  //   platform: wifi_info
  //   ip_address:
  //     name: IP Address
  //     disabled_by_default: false
  //     id: wifi_info_ipaddresswifiinfo_id
  //     entity_category: diagnostic
  //     update_interval: 1s
  //   ssid:
  //     name: Connected SSID
  //     disabled_by_default: false
  //     id: wifi_info_ssidwifiinfo_id
  //     entity_category: diagnostic
  //     update_interval: 1s
  //   mac_address:
  //     name: Mac Address
  //     disabled_by_default: false
  //     id: wifi_info_macaddresswifiinfo_id
  //     entity_category: diagnostic
  wifi_info_ssidwifiinfo_id = new wifi_info::SSIDWiFiInfo();
  App.register_text_sensor(wifi_info_ssidwifiinfo_id);
  wifi_info_ssidwifiinfo_id->set_name("Connected SSID");
  wifi_info_ssidwifiinfo_id->set_object_id("connected_ssid");
  wifi_info_ssidwifiinfo_id->set_disabled_by_default(false);
  wifi_info_ssidwifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_ssidwifiinfo_id->set_update_interval(1000);
  wifi_info_ssidwifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_ssidwifiinfo_id);
  wifi_info_macaddresswifiinfo_id = new wifi_info::MacAddressWifiInfo();
  App.register_text_sensor(wifi_info_macaddresswifiinfo_id);
  wifi_info_macaddresswifiinfo_id->set_name("Mac Address");
  wifi_info_macaddresswifiinfo_id->set_object_id("mac_address");
  wifi_info_macaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_macaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_macaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_macaddresswifiinfo_id);
  wifi_info_ipaddresswifiinfo_id = new wifi_info::IPAddressWiFiInfo();
  App.register_text_sensor(wifi_info_ipaddresswifiinfo_id);
  wifi_info_ipaddresswifiinfo_id->set_name("IP Address");
  wifi_info_ipaddresswifiinfo_id->set_object_id("ip_address");
  wifi_info_ipaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_ipaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_ipaddresswifiinfo_id->set_update_interval(1000);
  wifi_info_ipaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_ipaddresswifiinfo_id);
  // binary_sensor.status:
  //   platform: status
  //   name: Status
  //   disabled_by_default: false
  //   id: status_statusbinarysensor_id
  //   entity_category: diagnostic
  //   device_class: connectivity
  status_statusbinarysensor_id = new status::StatusBinarySensor();
  App.register_binary_sensor(status_statusbinarysensor_id);
  status_statusbinarysensor_id->set_name("Status");
  status_statusbinarysensor_id->set_object_id("status");
  status_statusbinarysensor_id->set_disabled_by_default(false);
  status_statusbinarysensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  status_statusbinarysensor_id->set_device_class("connectivity");
  status_statusbinarysensor_id->set_trigger_on_initial_state(false);
  status_statusbinarysensor_id->set_component_source("status.binary_sensor");
  App.register_component(status_statusbinarysensor_id);
  // switch.restart:
  //   platform: restart
  //   name: Restart
  //   disabled_by_default: false
  //   restore_mode: ALWAYS_OFF
  //   id: restart_restartswitch_id
  //   entity_category: config
  //   icon: mdi:restart
  restart_restartswitch_id = new restart::RestartSwitch();
  App.register_switch(restart_restartswitch_id);
  restart_restartswitch_id->set_name("Restart");
  restart_restartswitch_id->set_object_id("restart");
  restart_restartswitch_id->set_disabled_by_default(false);
  restart_restartswitch_id->set_icon("mdi:restart");
  restart_restartswitch_id->set_entity_category(::ENTITY_CATEGORY_CONFIG);
  restart_restartswitch_id->set_restore_mode(switch_::SWITCH_ALWAYS_OFF);
  restart_restartswitch_id->set_component_source("restart.switch");
  App.register_component(restart_restartswitch_id);
  // md5:
  // socket:
  //   implementation: bsd_sockets
  // globals:
  //   id: uart_buffer
  //   type: std::vector<uint8_t>
  //   restore_value: false
  uart_buffer = new globals::GlobalsComponent<std::vector<uint8_t>>();
  uart_buffer->set_component_source("globals");
  App.register_component(uart_buffer);
  // globals:
  //   id: last_tvoc
  //   type: float
  //   restore_value: false
  //   initial_value: '-999.0'
  last_tvoc = new globals::GlobalsComponent<float>(-999.0);
  last_tvoc->set_component_source("globals");
  App.register_component(last_tvoc);
  // globals:
  //   id: last_hcho
  //   type: float
  //   restore_value: false
  //   initial_value: '-999.0'
  last_hcho = new globals::GlobalsComponent<float>(-999.0);
  last_hcho->set_component_source("globals");
  App.register_component(last_hcho);
  // globals:
  //   id: last_co2
  //   type: float
  //   restore_value: false
  //   initial_value: '-999.0'
  last_co2 = new globals::GlobalsComponent<float>(-999.0);
  last_co2->set_component_source("globals");
  App.register_component(last_co2);
  lambdaaction_id_3 = new LambdaAction<>([=]() -> void {
      #line 104 "voc-esp32c3.yaml"
       
      int available = uart_bus->available();
      if (available > 0) {
        ESP_LOGD("voc_uart", "UART缓冲区有 %d 字节可用数据", available);
      
         
        uint8_t buffer[64];
        int bytes_read = 0;
      
         
        bytes_read = uart_bus->read_array(buffer, std::min(available, 64));
      
        if (bytes_read > 0) {
           
          for (int i = 0; i < bytes_read; i++) {
            uart_buffer->value().push_back(buffer[i]);
          }
      
           
          ESP_LOGI("voc_uart", "接收到 %d 字节数据:", bytes_read);
          std::string hex_data = "";
          for (int i = 0; i < bytes_read; i++) {
            char hex_str[4];
            sprintf(hex_str, "%02X ", buffer[i]);
            hex_data += hex_str;
          }
          ESP_LOGI("voc_uart", "原始数据: %s", hex_data.c_str());
      
           
          auto &buf = uart_buffer->value();
          for (size_t i = 0; i <= buf.size() - 9; i++) {
            if (buf[i] == 0x2C && buf[i + 1] == 0xE4) {
              ESP_LOGI("voc_uart", "在位置 %d 找到帧头 0x2C 0xE4", i);
      
               
              uint8_t frame[9];
              for (int j = 0; j < 9; j++) {
                frame[j] = buf[i + j];
              }
      
              ESP_LOGI("voc_uart", "提取的完整帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                       frame[0], frame[1], frame[2], frame[3], frame[4],
                       frame[5], frame[6], frame[7], frame[8]);
      
               
              uint8_t checksum = 0;
              for (int j = 0; j < 8; j++) {
                checksum += frame[j];
              }
      
              if ((checksum & 0xFF) == frame[8]) {
                ESP_LOGI("voc_uart", "找到有效数据帧，开始解析");
      
                 
                ESP_LOGI("voc_uart", "原始数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                         frame[0], frame[1], frame[2], frame[3], frame[4],
                         frame[5], frame[6], frame[7], frame[8]);
      
                 
                 
                uint16_t tvoc_raw = frame[2] * 256 + frame[3];
                float tvoc = (float)tvoc_raw * 0.001;
      
                 
                uint16_t hcho_raw = frame[4] * 256 + frame[5];
                float hcho = (float)hcho_raw * 0.001;
      
                 
                uint16_t co2_raw = frame[6] * 256 + frame[7];
                float co2 = (float)co2_raw * 0.001;
      
                ESP_LOGI("voc_uart", "解析结果 - TVOC原始值: %d, 甲醛原始值: %d, CO₂原始值: %d",
                         tvoc_raw, hcho_raw, co2_raw);
      
                 
                if (tvoc >= 0.0 && tvoc <= 10.0) {   
                  if (last_tvoc->value() == -999.0 || abs(tvoc - last_tvoc->value()) >= 0.001) {
                    ESP_LOGI("voc_uart", "TVOC: %.3f mg/m³", tvoc);
                    voc_tvoc->publish_state(tvoc);
                    last_tvoc->value() = tvoc;
                  }
                }
      
                if (hcho >= 0.0 && hcho <= 1.0) {   
                  if (last_hcho->value() == -999.0 || abs(hcho - last_hcho->value()) >= 0.001) {
                    ESP_LOGI("voc_uart", "甲醛: %.3f mg/m³", hcho);
                    voc_hcho->publish_state(hcho);
                    last_hcho->value() = hcho;
                  }
                }
      
                if (co2 >= 0.0 && co2 <= 5.0) {   
                  if (last_co2->value() == -999.0 || abs(co2 - last_co2->value()) >= 0.001) {
                    ESP_LOGI("voc_uart", "CO₂: %.3f mg/m³", co2);
                    voc_co2->publish_state(co2);
                    last_co2->value() = co2;
                  }
                }
              } else {
                 
                ESP_LOGW("voc_uart", "校验和错误，丢弃数据帧");
                ESP_LOGW("voc_uart", "原始数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                         frame[0], frame[1], frame[2], frame[3], frame[4],
                         frame[5], frame[6], frame[7], frame[8]);
                ESP_LOGW("voc_uart", "计算校验和: 0x%02X, 接收校验和: 0x%02X",
                         (checksum & 0xFF), frame[8]);
                ESP_LOGW("voc_uart", "校验和计算过程: %02X+%02X+%02X+%02X+%02X+%02X+%02X+%02X = 0x%02X",
                         frame[0], frame[1], frame[2], frame[3], frame[4],
                         frame[5], frame[6], frame[7], checksum);
              }
      
               
              buf.erase(buf.begin(), buf.begin() + i + 9);
              break;
            }
          }
      
           
          if (buf.size() >= 9) {
            bool found_frame = false;
            for (size_t i = 0; i <= buf.size() - 9; i++) {
              if (buf[i] == 0x2C && buf[i + 1] == 0xE4) {
                found_frame = true;
                break;
              }
            }
            if (!found_frame) {
              ESP_LOGW("voc_uart", "缓冲区中未找到有效帧头，当前缓冲区大小: %d", buf.size());
              std::string debug_data = "";
              for (size_t i = 0; i < std::min(buf.size(), (size_t)20); i++) {
                char hex_str[4];
                sprintf(hex_str, "%02X ", buf[i]);
                debug_data += hex_str;
              }
              ESP_LOGW("voc_uart", "缓冲区前20字节: %s", debug_data.c_str());
            }
          }
      
           
          if (buf.size() > 100) {
            ESP_LOGW("voc_uart", "缓冲区过长(%d字节)，清空缓冲区", buf.size());
            buf.clear();
          }
        }
      }
  });
  automation_id_3->add_actions({lambdaaction_id_3});
  interval_intervaltrigger_id->set_update_interval(2000);
  interval_intervaltrigger_id->set_startup_delay(0);
  // =========== AUTO GENERATED CODE END ============
  App.setup();
}

void loop() {
  App.loop();
}
