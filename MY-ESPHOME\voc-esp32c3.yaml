esphome:
  name: myesp32c3
  friendly_name: "VOC-CO2-HCHO ESP32-C3 Sensor"

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino

# 从secrets.yaml文件中读取WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  
  # WiFi连接失败时的处理
  ap:
    ssid: "ESP32C3-VOC Fallback Hotspot"
    password: "12345678"

  # WiFi连接状态回调
  on_connect:
    - logger.log: "WiFi连接成功！"
  on_disconnect:
    - logger.log: "WiFi连接断开！"

# UART配置 - 用于读取VOC-CO2-HCHO传感器数据
uart:
  id: uart_bus
  tx_pin: GPIO0  # TX引脚 - 接传感器RX
  rx_pin: GPIO1  # RX引脚 - 接传感器TX
  baud_rate: 9600  # VOC传感器波特率
  data_bits: 8
  parity: NONE
  stop_bits: 1
  rx_buffer_size: 512  # 增加接收缓冲区大小

# 全局变量存储VOC传感器数据和上次数值
globals:
  - id: uart_buffer
    type: std::vector<uint8_t>
    restore_value: no
  - id: last_tvoc
    type: float
    restore_value: no
    initial_value: '-999.0'  # 初始值设为不可能的TVOC浓度
  - id: last_hcho
    type: float
    restore_value: no
    initial_value: '-999.0'  # 初始值设为不可能的甲醛浓度
  - id: last_co2
    type: float
    restore_value: no
    initial_value: '-999.0'  # 初始值设为不可能的CO2浓度

sensor:
  # VOC-CO2-HCHO传感器数据
  - platform: template
    name: "TVOC"
    id: voc_tvoc
    unit_of_measurement: "mg/m³"
    accuracy_decimals: 3
    device_class: "volatile_organic_compounds"
    state_class: "measurement"
    icon: "mdi:chemical-weapon"

  - platform: template
    name: "Formaldehyde"
    id: voc_hcho
    unit_of_measurement: "mg/m³"
    accuracy_decimals: 3
    device_class: "volatile_organic_compounds"
    state_class: "measurement"
    icon: "mdi:molecule"

  - platform: template
    name: "CO2"
    id: voc_co2
    unit_of_measurement: "mg/m³"
    accuracy_decimals: 3
    device_class: "carbon_dioxide"
    state_class: "measurement"
    icon: "mdi:molecule-co2"

  # WiFi信号强度传感器
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  # 设备运行时间传感器
  - platform: uptime
    name: "Uptime"
    update_interval: 60s

  # ESP32-C3内部温度传感器
  - platform: internal_temperature
    name: "ESP32-C3 Internal Temperature"
    update_interval: 60s

# VOC传感器数据处理间隔
interval:
  - interval: 2000ms  # 每2秒检查一次UART数据
    then:
      - lambda: |-
          // 检查UART是否有可用数据
          int available = id(uart_bus).available();
          if (available > 0) {
            ESP_LOGD("voc_uart", "UART缓冲区有 %d 字节可用数据", available);

            // 读取所有可用数据到缓冲区
            uint8_t buffer[64];
            int bytes_read = 0;

            // 使用read_array方法读取数据
            bytes_read = id(uart_bus).read_array(buffer, std::min(available, 64));

            if (bytes_read > 0) {
              // 添加到全局缓冲区
              for (int i = 0; i < bytes_read; i++) {
                id(uart_buffer).push_back(buffer[i]);
              }

              // 详细输出接收到的原始数据
              ESP_LOGI("voc_uart", "接收到 %d 字节数据:", bytes_read);
              std::string hex_data = "";
              for (int i = 0; i < bytes_read; i++) {
                char hex_str[4];
                sprintf(hex_str, "%02X ", buffer[i]);
                hex_data += hex_str;
              }
              ESP_LOGI("voc_uart", "原始数据: %s", hex_data.c_str());

              // 查找完整的数据帧 (9字节，以0x2C 0xE4开头)
              auto &buf = id(uart_buffer);
              for (size_t i = 0; i <= buf.size() - 9; i++) {
                if (buf[i] == 0x2C && buf[i + 1] == 0xE4) {
                  ESP_LOGI("voc_uart", "在位置 %d 找到帧头 0x2C 0xE4", i);

                  // 找到帧头，提取9字节数据帧
                  uint8_t frame[9];
                  for (int j = 0; j < 9; j++) {
                    frame[j] = buf[i + j];
                  }

                  ESP_LOGI("voc_uart", "提取的完整帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                           frame[0], frame[1], frame[2], frame[3], frame[4],
                           frame[5], frame[6], frame[7], frame[8]);

                  // 验证校验和 (B1+B2+...+B8的低8位)
                  uint8_t checksum = 0;
                  for (int j = 0; j < 8; j++) {
                    checksum += frame[j];
                  }

                  if ((checksum & 0xFF) == frame[8]) {
                    ESP_LOGI("voc_uart", "找到有效数据帧，开始解析");

                    // 详细输出原始数据帧
                    ESP_LOGI("voc_uart", "原始数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                             frame[0], frame[1], frame[2], frame[3], frame[4],
                             frame[5], frame[6], frame[7], frame[8]);

                    // 解析VOC数据
                    // TVOC浓度 (mg/m³): (B3*256 + B4) × 0.001
                    uint16_t tvoc_raw = frame[2] * 256 + frame[3];
                    float tvoc = (float)tvoc_raw * 0.001;

                    // 甲醛浓度 (mg/m³): (B5*256 + B6) × 0.001
                    uint16_t hcho_raw = frame[4] * 256 + frame[5];
                    float hcho = (float)hcho_raw * 0.001;

                    // CO₂浓度 (mg/m³): (B7*256 + B8) × 0.001
                    uint16_t co2_raw = frame[6] * 256 + frame[7];
                    float co2 = (float)co2_raw * 0.001;

                    ESP_LOGI("voc_uart", "解析结果 - TVOC原始值: %d, 甲醛原始值: %d, CO₂原始值: %d",
                             tvoc_raw, hcho_raw, co2_raw);

                    // 数据有效性检查和发布
                    if (tvoc >= 0.0 && tvoc <= 10.0) {  // TVOC合理范围
                      if (id(last_tvoc) == -999.0 || abs(tvoc - id(last_tvoc)) >= 0.001) {
                        ESP_LOGI("voc_uart", "TVOC: %.3f mg/m³", tvoc);
                        id(voc_tvoc).publish_state(tvoc);
                        id(last_tvoc) = tvoc;
                      }
                    }

                    if (hcho >= 0.0 && hcho <= 1.0) {  // 甲醛合理范围
                      if (id(last_hcho) == -999.0 || abs(hcho - id(last_hcho)) >= 0.001) {
                        ESP_LOGI("voc_uart", "甲醛: %.3f mg/m³", hcho);
                        id(voc_hcho).publish_state(hcho);
                        id(last_hcho) = hcho;
                      }
                    }

                    if (co2 >= 0.0 && co2 <= 5.0) {  // CO2合理范围
                      if (id(last_co2) == -999.0 || abs(co2 - id(last_co2)) >= 0.001) {
                        ESP_LOGI("voc_uart", "CO₂: %.3f mg/m³", co2);
                        id(voc_co2).publish_state(co2);
                        id(last_co2) = co2;
                      }
                    }
                  } else {
                    // 详细输出校验和错误的原始数据
                    ESP_LOGW("voc_uart", "校验和错误，丢弃数据帧");
                    ESP_LOGW("voc_uart", "原始数据帧: %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                             frame[0], frame[1], frame[2], frame[3], frame[4],
                             frame[5], frame[6], frame[7], frame[8]);
                    ESP_LOGW("voc_uart", "计算校验和: 0x%02X, 接收校验和: 0x%02X",
                             (checksum & 0xFF), frame[8]);
                    ESP_LOGW("voc_uart", "校验和计算过程: %02X+%02X+%02X+%02X+%02X+%02X+%02X+%02X = 0x%02X",
                             frame[0], frame[1], frame[2], frame[3], frame[4],
                             frame[5], frame[6], frame[7], checksum);
                  }

                  // 移除已处理的数据
                  buf.erase(buf.begin(), buf.begin() + i + 9);
                  break;
                }
              }

              // 如果没有找到有效帧头，输出当前缓冲区内容用于调试
              if (buf.size() >= 9) {
                bool found_frame = false;
                for (size_t i = 0; i <= buf.size() - 9; i++) {
                  if (buf[i] == 0x2C && buf[i + 1] == 0xE4) {
                    found_frame = true;
                    break;
                  }
                }
                if (!found_frame) {
                  ESP_LOGW("voc_uart", "缓冲区中未找到有效帧头，当前缓冲区大小: %d", buf.size());
                  std::string debug_data = "";
                  for (size_t i = 0; i < std::min(buf.size(), (size_t)20); i++) {
                    char hex_str[4];
                    sprintf(hex_str, "%02X ", buf[i]);
                    debug_data += hex_str;
                  }
                  ESP_LOGW("voc_uart", "缓冲区前20字节: %s", debug_data.c_str());
                }
              }

              // 清理过长的缓冲区
              if (buf.size() > 100) {
                ESP_LOGW("voc_uart", "缓冲区过长(%d字节)，清空缓冲区", buf.size());
                buf.clear();
              }
            }
          }

  # VOC传感器不需要发送命令，它会自动发送数据

# # 状态LED指示
# light:
#   - platform: status_led
#     name: "Status LED"
#     pin: GPIO13  # ESP32-C3开发板上的LED引脚

# 文本传感器
text_sensor:
  # WiFi信息
  - platform: wifi_info
    ip_address:
      name: "IP Address"
    ssid:
      name: "Connected SSID"
    mac_address:
      name: "Mac Address"

# 二进制传感器
binary_sensor:
  # 设备状态
  - platform: status
    name: "Status"

# 开关控制
switch:
  # 重启开关
  - platform: restart
    name: "Restart"

# 日志配置
logger:
  baud_rate: 115200
  level: INFO  # 显示传感器数据
  logs:
    voc_uart: INFO    # 显示VOC传感器数据
    uart_debug: WARN  # 减少UART调试信息
    uart: WARN        # 减少UART日志
    wifi: WARN        # 减少WiFi日志
    api: WARN         # 减少API日志
    ota: WARN         # 减少OTA日志
    web_server: WARN  # 减少Web服务器日志

# OTA更新
ota:
  platform: esphome

# API配置
api:

# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password
