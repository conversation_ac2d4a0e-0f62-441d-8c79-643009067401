; Auto generated code by esphome

[common]
lib_deps =
build_flags =
upload_flags =

; ========== AUTO GENERATED CODE BEGIN ===========
[platformio]
description = ESPHome 2025.7.2
[env:voc-myesp32c3]
board = airm2m_core_esp32c3
board_build.partitions = partitions.csv
board_upload.flash_size = 4MB
build_flags =
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DARDUINO_USB_MODE=1
    -DESPHOME_LOG_LEVEL=ESPHOME_LOG_LEVEL_INFO
    -DUSE_ARDUINO
    -DUSE_ESP32
    -DUSE_ESP32_FRAMEWORK_ARDUINO
    -DUSE_ESP32_VARIANT_ESP32C3
    -Wno-sign-compare
    -Wno-unused-but-set-variable
    -Wno-unused-variable
    -fno-exceptions
    -std=gnu++20
build_unflags =
    -std=gnu++11
    -std=gnu++14
    -std=gnu++17
    -std=gnu++23
    -std=gnu++2a
    -std=gnu++2b
    -std=gnu++2c
extra_scripts =
    post:post_build.py
    pre:cxx_flags.py
framework = arduino
lib_compat_mode = strict
lib_deps =
    Networking
    ESP32Async/AsyncTCP@3.4.5
    WiFi
    FS
    Update
    ESP32Async/ESPAsyncWebServer@3.7.10
    ESPmDNS
    bblanchon/ArduinoJson@7.4.2
    ${common.lib_deps}
lib_ldf_mode = off
platform = https://github.com/pioarduino/platform-espressif32/releases/download/53.03.13/platform-espressif32.zip
platform_packages =
    pioarduino/framework-arduinoespressif32@https://github.com/espressif/arduino-esp32/releases/download/3.1.3/esp32-3.1.3.zip
; =========== AUTO GENERATED CODE END ============

